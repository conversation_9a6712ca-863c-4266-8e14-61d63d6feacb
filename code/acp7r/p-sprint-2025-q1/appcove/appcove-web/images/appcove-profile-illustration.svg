<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions for depth -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2c5f7a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e4a5f;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="tealGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00bfa5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a693;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lightTealGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4dd0e1;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00bfa5;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- Circular mask for profile image -->
    <clipPath id="circularClip">
      <circle cx="200" cy="200" r="80"/>
    </clipPath>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="400" fill="#f8fafc"/>
  
  <!-- Left Section: Company Profile Area -->
  <g id="leftSection">
    <!-- Background shape for left section -->
    <rect x="0" y="0" width="400" height="400" fill="url(#blueGradient)" opacity="0.05"/>
    
    <!-- Geometric background elements -->
    <polygon points="0,0 120,0 80,60 0,40" fill="url(#blueGradient)" opacity="0.1"/>
    <polygon points="0,360 0,400 60,400 40,340" fill="url(#tealGradient)" opacity="0.15"/>
    
    <!-- Company profile image placeholder with circular frame -->
    <g id="profileImageContainer" transform="translate(200, 200)">
      <!-- Outer ring -->
      <circle cx="0" cy="0" r="90" fill="none" stroke="url(#tealGradient)" stroke-width="3"/>
      
      <!-- Inner background circle -->
      <circle cx="0" cy="0" r="80" fill="url(#blueGradient)"/>
      
      <!-- Profile image reference (this would be replaced with actual image) -->
      <g clip-path="url(#circularClip)" transform="translate(-200, -200)">
        <!-- Placeholder for AppCove.png - represented as geometric shapes -->
        <circle cx="200" cy="200" r="80" fill="url(#blueGradient)"/>
        
        <!-- Stylized figure representation -->
        <g transform="translate(200, 200)">
          <!-- Head -->
          <circle cx="0" cy="-25" r="18" fill="#ffffff" opacity="0.9"/>
          
          <!-- Body/Suit -->
          <rect x="-20" y="-5" width="40" height="45" rx="8" fill="url(#tealGradient)"/>
          
          <!-- Tie -->
          <polygon points="-3,-5 3,-5 6,15 -6,15" fill="#ffffff" opacity="0.8"/>
          
          <!-- Collar -->
          <polygon points="-15,-5 -8,-15 8,-15 15,-5 8,-5 -8,-5" fill="#ffffff" opacity="0.9"/>
        </g>
      </g>
      
      <!-- Decorative dots around profile -->
      <circle cx="65" cy="-45" r="4" fill="url(#tealGradient)" opacity="0.6"/>
      <circle cx="-65" cy="45" r="4" fill="url(#tealGradient)" opacity="0.6"/>
      <circle cx="45" cy="65" r="3" fill="url(#blueGradient)" opacity="0.4"/>
    </g>
    
    <!-- Company name text area indicator -->
    <rect x="50" y="320" width="120" height="4" rx="2" fill="url(#blueGradient)" opacity="0.3"/>
    <rect x="50" y="330" width="80" height="3" rx="1.5" fill="url(#tealGradient)" opacity="0.4"/>
    
    <!-- Geometric accent elements -->
    <polygon points="320,50 380,50 380,80 350,110 320,80" fill="url(#lightTealGradient)"/>
    <circle cx="350" cy="350" r="25" fill="url(#tealGradient)" opacity="0.2"/>
  </g>
  
  <!-- Divider line -->
  <line x1="400" y1="50" x2="400" y2="350" stroke="url(#tealGradient)" stroke-width="2" opacity="0.3"/>
  
  <!-- Right Section: Circular Curved Design Element -->
  <g id="rightSection">
    <!-- Background shape for right section -->
    <rect x="400" y="0" width="400" height="400" fill="url(#tealGradient)" opacity="0.03"/>
    
    <!-- Main circular curved design -->
    <g transform="translate(600, 200)">
      <!-- Large background circle -->
      <circle cx="0" cy="0" r="120" fill="none" stroke="url(#blueGradient)" stroke-width="2" opacity="0.2"/>
      
      <!-- Medium circle with gradient fill -->
      <circle cx="0" cy="0" r="80" fill="url(#lightTealGradient)"/>
      
      <!-- Inner circle -->
      <circle cx="0" cy="0" r="50" fill="none" stroke="url(#tealGradient)" stroke-width="3"/>
      
      <!-- Central geometric element -->
      <g>
        <!-- Diamond shape -->
        <polygon points="0,-25 25,0 0,25 -25,0" fill="url(#blueGradient)"/>
        
        <!-- Inner diamond -->
        <polygon points="0,-15 15,0 0,15 -15,0" fill="#ffffff" opacity="0.9"/>
        
        <!-- Center dot -->
        <circle cx="0" cy="0" r="4" fill="url(#tealGradient)"/>
      </g>
      
      <!-- Orbital elements -->
      <g>
        <!-- Rotating elements around the center -->
        <circle cx="35" cy="0" r="6" fill="url(#tealGradient)" opacity="0.8"/>
        <circle cx="-35" cy="0" r="6" fill="url(#blueGradient)" opacity="0.6"/>
        <circle cx="0" cy="35" r="5" fill="url(#tealGradient)" opacity="0.7"/>
        <circle cx="0" cy="-35" r="5" fill="url(#blueGradient)" opacity="0.5"/>
      </g>
      
      <!-- Curved accent lines -->
      <path d="M -60,-60 Q 0,-80 60,-60" fill="none" stroke="url(#tealGradient)" stroke-width="2" opacity="0.6"/>
      <path d="M -60,60 Q 0,80 60,60" fill="none" stroke="url(#blueGradient)" stroke-width="2" opacity="0.4"/>
    </g>
    
    <!-- Corner geometric elements -->
    <polygon points="750,50 800,50 800,100 780,80" fill="url(#blueGradient)" opacity="0.15"/>
    <polygon points="420,350 450,350 450,380 420,380" fill="url(#tealGradient)" opacity="0.2"/>
    
    <!-- Floating geometric shapes -->
    <circle cx="500" cy="100" r="8" fill="url(#tealGradient)" opacity="0.4"/>
    <polygon points="720,320 730,310 740,320 730,330" fill="url(#blueGradient)" opacity="0.3"/>
    
    <!-- Curved connecting elements -->
    <path d="M 450,150 Q 500,120 550,150" fill="none" stroke="url(#lightTealGradient)" stroke-width="3" opacity="0.5"/>
    <path d="M 650,250 Q 700,280 750,250" fill="none" stroke="url(#tealGradient)" stroke-width="2" opacity="0.6"/>
  </g>
  
  <!-- Subtle connecting elements between sections -->
  <g opacity="0.3">
    <path d="M 380,180 Q 420,160 460,180" fill="none" stroke="url(#tealGradient)" stroke-width="1"/>
    <path d="M 380,220 Q 420,240 460,220" fill="none" stroke="url(#blueGradient)" stroke-width="1"/>
  </g>
</svg>
